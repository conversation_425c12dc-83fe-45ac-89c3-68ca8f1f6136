"""
技术反转选股策略
基于601111在2025年4月7日和4月30日买入点特征设计
"""
import logging
from datetime import datetime, timedelta
from typing import List, Dict
from statistics import mean

from ..core.interfaces.strategy import ISelectionStrategy
from ..core.interfaces.data_access import IDataAccess
from ..utils.date_utils import get_trading_days


class TechnicalReversalStrategy(ISelectionStrategy):
    """技术反转选股策略"""

    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.config = {
            # RSI条件
            'rsi_min': 20.0,  # RSI最小值
            'rsi_max': 50.0,  # RSI最大值

            # 交易量条件
            'volume_ratio_min': 1.2,  # 相对10日均量最小倍数
            'volume_ratio_max': 5.0,  # 相对10日均量最大倍数
            'min_volume': 300000,  # 最小交易量（手）

            # 价格位置条件
            'price_position_min': 0.0,  # 20日区间最小位置
            'price_position_max': 0.6,  # 20日区间最大位置

            # 布林带条件
            'bb_position_min': -0.3,  # 布林带最小位置
            'bb_position_max': 0.5,   # 布林带最大位置

            # 均线关系条件
            'ma5_deviation_min': -10.0,  # 相对MA5最小偏离度(%)
            'ma5_deviation_max': 2.0,    # 相对MA5最大偏离度(%)
            'ma10_deviation_min': -12.0, # 相对MA10最小偏离度(%)
            'ma10_deviation_max': 2.0,   # 相对MA10最大偏离度(%)

            # 前期走势条件
            'prev_5d_return_min': -15.0,  # 前5日最小累计收益(%)
            'prev_5d_return_max': 3.0,    # 前5日最大累计收益(%)

            # MACD条件
            'macd_histogram_max': 0.1,  # MACD柱状图最大值

            # 基本过滤条件
            'min_price': 3.0,  # 最低价格
            'max_price': 50.0,  # 最高价格
            'exclude_st': True,  # 排除ST股票
            'baseline_days': 20,  # 基准期天数
            'max_results': 15  # 最大结果数量
        }

    def get_strategy_name(self) -> str:
        """获取策略名称"""
        return "技术反转策略"

    def get_strategy_description(self) -> str:
        """获取策略描述"""
        return "基于技术指标识别处于底部区域且有反转潜力的股票，适合短期反弹操作"

    def get_config(self) -> Dict:
        """获取策略配置"""
        return self.config.copy()

    def set_config(self, config: Dict) -> None:
        """设置策略配置"""
        self.config.update(config)

    def validate_config(self, config: Dict) -> bool:
        """验证策略配置"""
        required_keys = [
            'rsi_min', 'rsi_max', 'volume_ratio_min', 'volume_ratio_max',
            'price_position_min', 'price_position_max', 'bb_position_min', 'bb_position_max',
            'ma5_deviation_min', 'ma5_deviation_max', 'prev_5d_return_min', 'prev_5d_return_max',
            'min_price', 'max_price', 'baseline_days', 'max_results'
        ]

        for key in required_keys:
            if key not in config:
                return False

        # 验证数值范围
        if config['rsi_min'] >= config['rsi_max']:
            return False
        if config['volume_ratio_min'] >= config['volume_ratio_max']:
            return False
        if config['price_position_min'] >= config['price_position_max']:
            return False
        if config['min_price'] <= 0 or config['max_price'] <= config['min_price']:
            return False
        if config['baseline_days'] <= 0:
            return False
        if config['max_results'] <= 0:
            return False

        return True

    def execute(self, data_access: IDataAccess) -> List[Dict]:
        """执行选股策略"""
        try:
            self.logger.info(f"开始执行{self.get_strategy_name()}")

            # 获取所有股票代码
            stock_codes = data_access.get_all_stock_codes()
            self.logger.info(f"获取到{len(stock_codes)}只股票")

            # 计算日期范围
            end_date = datetime.now()
            start_date = end_date - timedelta(days=self.config['baseline_days'] + 10)  # 多取几天以防节假日

            results = []
            processed_count = 0

            for stock_code in stock_codes:
                try:
                    # 获取股票基本信息
                    stock_info = data_access.get_stock_info(stock_code)
                    if not stock_info:
                        continue

                    # 过滤ST股票
                    if self.config['exclude_st'] and self._is_st_stock(stock_info['stock_name']):
                        continue

                    # 获取交易数据
                    trading_data = data_access.get_stock_data(stock_code, start_date, end_date)
                    if len(trading_data) < self.config['baseline_days']:
                        continue

                    # 分析技术反转信号
                    reversal_result = self._analyze_technical_reversal(stock_code, stock_info, trading_data)
                    if reversal_result:
                        results.append(reversal_result)

                    processed_count += 1
                    if processed_count % 100 == 0:
                        self.logger.info(f"已处理{processed_count}只股票，当前选中{len(results)}只")

                except Exception as e:
                    self.logger.warning(f"处理股票{stock_code}时出错: {str(e)}")
                    continue

            # 按评分排序并限制结果数量
            results.sort(key=lambda x: x['score'], reverse=True)
            results = results[:self.config['max_results']]

            self.logger.info(f"选股完成，共选中{len(results)}只股票")
            return results

        except Exception as e:
            self.logger.error(f"执行选股策略失败: {str(e)}")
            raise

    def _is_st_stock(self, stock_name: str) -> bool:
        """判断是否为ST股票"""
        st_keywords = ['ST', '*ST', 'PT']
        return any(keyword in stock_name for keyword in st_keywords)

    def _analyze_technical_reversal(self, stock_code: str, stock_info: Dict, trading_data: List[Dict]) -> Dict:
        """分析技术反转信号"""
        try:
            # 按日期排序
            trading_data.sort(key=lambda x: x['trade_date'])

            # 确保有足够的数据
            if len(trading_data) < self.config['baseline_days']:
                return None

            # 计算技术指标
            df_data = self._calculate_technical_indicators(trading_data)

            # 获取最新交易日数据
            latest_data = df_data[-1]

            # 基本价格过滤
            close_price = float(latest_data['close_price'])
            if close_price < self.config['min_price'] or close_price > self.config['max_price']:
                return None

            # 交易量过滤
            latest_volume = int(latest_data['volume'])
            if latest_volume < self.config['min_volume']:
                return None

            # 检查各项技术条件
            conditions = []
            score_components = []

            # 1. RSI条件
            rsi = latest_data['rsi']
            if self.config['rsi_min'] <= rsi <= self.config['rsi_max']:
                conditions.append(f"RSI({rsi:.1f})在合理区间")
                score_components.append(20)
            else:
                return None

            # 2. 交易量条件
            volume_ratio = latest_data['volume_ratio_10']
            if self.config['volume_ratio_min'] <= volume_ratio <= self.config['volume_ratio_max']:
                conditions.append(f"交易量放大{volume_ratio:.2f}倍")
                score_components.append(15)
            else:
                return None

            # 3. 价格位置条件
            price_position = latest_data['price_position_20']
            if self.config['price_position_min'] <= price_position <= self.config['price_position_max']:
                conditions.append(f"价格位置({price_position:.2f})适中")
                score_components.append(15)
            else:
                return None

            # 4. 布林带位置条件
            bb_position = latest_data['bb_position']
            if self.config['bb_position_min'] <= bb_position <= self.config['bb_position_max']:
                conditions.append(f"布林带位置({bb_position:.2f})合适")
                score_components.append(10)

            # 5. 均线关系条件
            ma5_deviation = latest_data['price_vs_ma5']
            ma10_deviation = latest_data['price_vs_ma10']

            if (self.config['ma5_deviation_min'] <= ma5_deviation <= self.config['ma5_deviation_max'] and
                self.config['ma10_deviation_min'] <= ma10_deviation <= self.config['ma10_deviation_max']):
                conditions.append(f"均线关系良好(MA5:{ma5_deviation:.1f}%,MA10:{ma10_deviation:.1f}%)")
                score_components.append(15)

            # 6. 前期走势条件
            prev_5d_return = latest_data['prev_5d_return']
            if self.config['prev_5d_return_min'] <= prev_5d_return <= self.config['prev_5d_return_max']:
                conditions.append(f"前期调整充分({prev_5d_return:.1f}%)")
                score_components.append(10)

            # 7. MACD条件
            macd_histogram = latest_data['macd_histogram']
            if macd_histogram <= self.config['macd_histogram_max']:
                conditions.append(f"MACD处于底部区域")
                score_components.append(10)

            # 至少满足5个条件才考虑
            if len(conditions) < 5:
                return None

            # 计算综合评分
            base_score = sum(score_components)

            # 额外加分项
            bonus_score = 0

            # RSI越接近30加分越多
            if rsi < 35:
                bonus_score += (35 - rsi) * 0.5

            # 交易量适度放大加分
            if 1.6 <= volume_ratio <= 2.0:
                bonus_score += 5

            # 价格位置越低加分越多
            if price_position < 0.3:
                bonus_score += (0.3 - price_position) * 10

            total_score = base_score + bonus_score

            # 生成选股原因
            reason = f"技术反转信号: {', '.join(conditions[:3])}"

            return {
                'stock_code': stock_code,
                'stock_name': stock_info['stock_name'],
                'score': round(total_score, 2),
                'reason': reason,
                'selection_date': datetime.now().date(),
                'close_price': close_price,
                'rsi': rsi,
                'volume_ratio': volume_ratio,
                'price_position': price_position,
                'bb_position': bb_position,
                'conditions_count': len(conditions)
            }

        except Exception as e:
            self.logger.warning(f"分析股票{stock_code}技术反转信号失败: {str(e)}")
            return None

    def _calculate_technical_indicators(self, trading_data: List[Dict]) -> List[Dict]:
        """计算技术指标"""
        # 转换数据格式
        data = []
        for item in trading_data:
            data.append({
                'trade_date': item['trade_date'],
                'open_price': float(item['open_price']),
                'high_price': float(item['high_price']),
                'low_price': float(item['low_price']),
                'close_price': float(item['close_price']),
                'volume': int(item['volume']),
                'turnover_rate': float(item.get('turnover_rate', 0))
            })

        # 计算移动平均线
        for i in range(len(data)):
            # MA5
            if i >= 4:
                ma5_sum = sum(data[j]['close_price'] for j in range(i-4, i+1))
                data[i]['ma5'] = ma5_sum / 5
            else:
                data[i]['ma5'] = data[i]['close_price']

            # MA10
            if i >= 9:
                ma10_sum = sum(data[j]['close_price'] for j in range(i-9, i+1))
                data[i]['ma10'] = ma10_sum / 10
            else:
                data[i]['ma10'] = data[i]['close_price']

            # MA20
            if i >= 19:
                ma20_sum = sum(data[j]['close_price'] for j in range(i-19, i+1))
                data[i]['ma20'] = ma20_sum / 20
            else:
                data[i]['ma20'] = data[i]['close_price']

            # 交易量移动平均
            if i >= 9:
                vol_ma10_sum = sum(data[j]['volume'] for j in range(i-9, i+1))
                data[i]['vol_ma10'] = vol_ma10_sum / 10
                data[i]['volume_ratio_10'] = data[i]['volume'] / data[i]['vol_ma10']
            else:
                data[i]['vol_ma10'] = data[i]['volume']
                data[i]['volume_ratio_10'] = 1.0

        # 计算RSI
        for i in range(len(data)):
            if i >= 14:
                gains = []
                losses = []
                for j in range(i-13, i+1):
                    if j > 0:
                        change = data[j]['close_price'] - data[j-1]['close_price']
                        if change > 0:
                            gains.append(change)
                            losses.append(0)
                        else:
                            gains.append(0)
                            losses.append(-change)

                avg_gain = mean(gains) if gains else 0
                avg_loss = mean(losses) if losses else 0

                if avg_loss == 0:
                    data[i]['rsi'] = 100
                else:
                    rs = avg_gain / avg_loss
                    data[i]['rsi'] = 100 - (100 / (1 + rs))
            else:
                data[i]['rsi'] = 50  # 默认值

        # 计算MACD
        for i in range(len(data)):
            if i >= 25:  # 需要足够的数据计算EMA
                # 简化的MACD计算
                ema12 = self._calculate_ema([data[j]['close_price'] for j in range(max(0, i-11), i+1)], 12)
                ema26 = self._calculate_ema([data[j]['close_price'] for j in range(max(0, i-25), i+1)], 26)
                data[i]['macd'] = ema12 - ema26

                # MACD信号线（9日EMA）
                if i >= 33:
                    macd_values = [data[j]['macd'] for j in range(max(0, i-8), i+1)]
                    data[i]['macd_signal'] = self._calculate_ema(macd_values, 9)
                    data[i]['macd_histogram'] = data[i]['macd'] - data[i]['macd_signal']
                else:
                    data[i]['macd_signal'] = data[i]['macd']
                    data[i]['macd_histogram'] = 0
            else:
                data[i]['macd'] = 0
                data[i]['macd_signal'] = 0
                data[i]['macd_histogram'] = 0

        # 计算布林带
        for i in range(len(data)):
            if i >= 19:
                prices = [data[j]['close_price'] for j in range(i-19, i+1)]
                bb_middle = mean(prices)
                variance = sum((p - bb_middle) ** 2 for p in prices) / len(prices)
                bb_std = variance ** 0.5

                data[i]['bb_middle'] = bb_middle
                data[i]['bb_upper'] = bb_middle + (bb_std * 2)
                data[i]['bb_lower'] = bb_middle - (bb_std * 2)

                if data[i]['bb_upper'] != data[i]['bb_lower']:
                    data[i]['bb_position'] = (data[i]['close_price'] - data[i]['bb_lower']) / (data[i]['bb_upper'] - data[i]['bb_lower'])
                else:
                    data[i]['bb_position'] = 0.5
            else:
                data[i]['bb_middle'] = data[i]['close_price']
                data[i]['bb_upper'] = data[i]['close_price']
                data[i]['bb_lower'] = data[i]['close_price']
                data[i]['bb_position'] = 0.5

        # 计算价格位置和其他指标
        for i in range(len(data)):
            # 20日价格位置
            if i >= 19:
                high_20 = max(data[j]['high_price'] for j in range(i-19, i+1))
                low_20 = min(data[j]['low_price'] for j in range(i-19, i+1))
                if high_20 != low_20:
                    data[i]['price_position_20'] = (data[i]['close_price'] - low_20) / (high_20 - low_20)
                else:
                    data[i]['price_position_20'] = 0.5
            else:
                data[i]['price_position_20'] = 0.5

            # 与均线的关系
            data[i]['price_vs_ma5'] = ((data[i]['close_price'] / data[i]['ma5']) - 1) * 100
            data[i]['price_vs_ma10'] = ((data[i]['close_price'] / data[i]['ma10']) - 1) * 100
            data[i]['price_vs_ma20'] = ((data[i]['close_price'] / data[i]['ma20']) - 1) * 100

            # 前5日收益
            if i >= 5:
                prev_5d_return = 0
                for j in range(i-4, i+1):
                    if j > 0:
                        daily_return = (data[j]['close_price'] / data[j-1]['close_price'] - 1) * 100
                        prev_5d_return += daily_return
                data[i]['prev_5d_return'] = prev_5d_return
            else:
                data[i]['prev_5d_return'] = 0

        return data

    def _calculate_ema(self, prices: List[float], period: int) -> float:
        """计算指数移动平均"""
        if not prices:
            return 0

        multiplier = 2 / (period + 1)
        ema = prices[0]

        for price in prices[1:]:
            ema = (price * multiplier) + (ema * (1 - multiplier))

        return ema
