"""
策略验证相关接口定义
"""
from abc import ABC, abstractmethod
from datetime import datetime
from typing import List, Dict, Optional
from dataclasses import dataclass


@dataclass
class ValidationConfig:
    """验证配置"""
    strategy_name: str  # 策略名称
    test_dates: List[datetime]  # 测试日期列表
    max_stocks_per_test: int = 20  # 每次测试最多选股数
    stock_pool: Optional[List[str]] = None  # 股票池，None表示全市场
    show_indicators: bool = True  # 是否显示详细指标
    save_results: bool = True  # 是否保存结果到数据库
    
    def __post_init__(self):
        """验证配置参数"""
        if not self.test_dates:
            raise ValueError("测试日期列表不能为空")
        if self.max_stocks_per_test <= 0:
            raise ValueError("每次测试最多选股数必须大于0")


@dataclass
class ValidationResult:
    """单次验证结果"""
    test_date: datetime  # 测试日期
    strategy_name: str  # 策略名称
    selected_stocks: List[Dict]  # 选中的股票及其指标
    total_candidates: int  # 候选股票总数
    selection_count: int  # 实际选中数量
    selection_rate: float  # 选股比例
    strategy_config: Dict  # 策略配置参数
    execution_time: float  # 执行耗时（秒）
    error_message: Optional[str] = None  # 错误信息


@dataclass
class ValidationSummary:
    """验证汇总结果"""
    config: ValidationConfig  # 验证配置
    results: List[ValidationResult]  # 所有验证结果
    total_tests: int  # 总测试次数
    successful_tests: int  # 成功测试次数
    failed_tests: int  # 失败测试次数
    avg_selection_count: float  # 平均选股数量
    avg_selection_rate: float  # 平均选股比例
    total_execution_time: float  # 总执行时间


class IStrategyValidator(ABC):
    """策略验证器抽象接口"""
    
    @abstractmethod
    def validate_strategy(self, config: ValidationConfig) -> ValidationSummary:
        """
        验证策略
        
        Args:
            config: 验证配置
            
        Returns:
            ValidationSummary: 验证汇总结果
        """
        pass
    
    @abstractmethod
    def validate_single_date(self, strategy_name: str, 
                           test_date: datetime,
                           max_stocks: int = 20,
                           stock_pool: Optional[List[str]] = None) -> ValidationResult:
        """
        验证单个日期
        
        Args:
            strategy_name: 策略名称
            test_date: 测试日期
            max_stocks: 最多选股数
            stock_pool: 股票池
            
        Returns:
            ValidationResult: 验证结果
        """
        pass
    
    @abstractmethod
    def save_validation_result(self, result: ValidationResult) -> bool:
        """
        保存验证结果
        
        Args:
            result: 验证结果
            
        Returns:
            bool: 保存是否成功
        """
        pass
    
    @abstractmethod
    def get_validation_history(self, strategy_name: str = None,
                             start_date: datetime = None,
                             end_date: datetime = None) -> List[ValidationResult]:
        """
        获取验证历史
        
        Args:
            strategy_name: 策略名称，None表示所有策略
            start_date: 开始日期，None表示不限制
            end_date: 结束日期，None表示不限制
            
        Returns:
            List[ValidationResult]: 验证结果列表
        """
        pass
    
    @abstractmethod
    def generate_validation_report(self, summary: ValidationSummary) -> str:
        """
        生成验证报告
        
        Args:
            summary: 验证汇总结果
            
        Returns:
            str: 验证报告文本
        """
        pass
